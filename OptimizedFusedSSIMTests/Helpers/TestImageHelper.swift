import Accelerate
import CoreGraphics
import Foundation

enum TestImageType {
    case solid(width: Int, height: Int, color: (r: UInt8, g: UInt8, b: UInt8, a: UInt8))
    case gradient(width: Int, height: Int)
}

class TestImageHelper {

    /// Loads a CGImage from the test bundle
    static func loadImageFromBundle(named filename: String) -> CGImage? {
        let testBundle = Bundle(for: TestImageHelper.self)

        // Try different path variations
        let searchPaths = [
            filename,
            "TestAssets/\(filename)",
            "images/\(filename)",
        ]

        for searchPath in searchPaths {
            if let imageURL = testBundle.url(forResource: searchPath, withExtension: nil) {
                if let imageSource = CGImageSourceCreateWithURL(imageURL as CFURL, nil),
                    let cgImage = CGImageSourceCreateImageAtIndex(imageSource, 0, nil)
                {
                    print(
                        "✅ Successfully loaded image: \(filename) (\(cgImage.width)x\(cgImage.height))"
                    )
                    return cgImage
                }
            }

            // Try without extension
            let nameWithoutExt = (searchPath as NSString).deletingPathExtension
            if let imageURL = testBundle.url(forResource: nameWithoutExt, withExtension: nil) {
                if let imageSource = CGImageSourceCreateWithURL(imageURL as CFURL, nil),
                    let cgImage = CGImageSourceCreateImageAtIndex(imageSource, 0, nil)
                {
                    print(
                        "✅ Successfully loaded image: \(filename) (\(cgImage.width)x\(cgImage.height))"
                    )
                    return cgImage
                }
            }
        }

        print("❌ Error: Could not load image '\(filename)' from test bundle")
        return nil
    }

    /// Measures execution time for a block of code
    static func measureTime<T>(
        operation: String,
        block: () throws -> T
    ) rethrows -> (result: T, timeMs: Double) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try block()
        let endTime = CFAbsoluteTimeGetCurrent()
        let timeMs = (endTime - startTime) * 1000.0

        print("⏱️  \(operation): \(String(format: "%.2f", timeMs))ms")
        return (result: result, timeMs: timeMs)
    }

    /// Creates a CGImage with a solid color.
    static func createImage(
        width: Int, height: Int, color: (r: UInt8, g: UInt8, b: UInt8, a: UInt8)
    ) -> CGImage? {
        let bitsPerComponent = 8
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let colorSpace = CGColorSpaceCreateDeviceRGB()

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
        // Create non-premultiplied RGBA data directly.
        // The ImageConverter will handle blending as per the architecture.
        for i in stride(from: 0, to: pixelData.count, by: bytesPerPixel) {
            pixelData[i] = color.r
            pixelData[i + 1] = color.g
            pixelData[i + 2] = color.b
            pixelData[i + 3] = color.a
        }

        // The data is not premultiplied.
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.last.rawValue)
        guard let dataProvider = CGDataProvider(data: Data(pixelData) as CFData),
            let cgImage = CGImage(
                width: width,
                height: height,
                bitsPerComponent: bitsPerComponent,
                bitsPerPixel: bytesPerPixel * 8,
                bytesPerRow: bytesPerRow,
                space: colorSpace,
                bitmapInfo: bitmapInfo,
                provider: dataProvider,
                decode: nil,
                shouldInterpolate: false,
                intent: .defaultIntent
            )
        else {
            return nil
        }

        return cgImage
    }

    /// Creates a "compressed" version of the image by adding noise.
    static func createCompressedVersion(of image: CGImage, noiseLevel: Float = 10.0) -> CGImage? {
        let width = image.width
        let height = image.height
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow

        // Create a context to get pixel data
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        var pixelData = [UInt8](repeating: 0, count: totalBytes)

        guard
            let context = CGContext(
                data: &pixelData,
                width: width,
                height: height,
                bitsPerComponent: 8,
                bytesPerRow: bytesPerRow,
                space: colorSpace,
                bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
            )
        else {
            return nil
        }

        context.draw(image, in: CGRect(x: 0, y: 0, width: width, height: height))

        // Add noise to simulate compression artifacts
        for i in stride(from: 0, to: pixelData.count, by: 4) {
            let noise = Int(Float.random(in: -noiseLevel...noiseLevel))
            pixelData[i] = UInt8(max(0, min(255, Int(pixelData[i]) + noise)))  // R
            pixelData[i + 1] = UInt8(max(0, min(255, Int(pixelData[i + 1]) + noise)))  // G
            pixelData[i + 2] = UInt8(max(0, min(255, Int(pixelData[i + 2]) + noise)))  // B
            // Leave alpha unchanged: pixelData[i+3]
        }

        guard let dataProvider = CGDataProvider(data: Data(pixelData) as CFData),
            let compressedImage = CGImage(
                width: width,
                height: height,
                bitsPerComponent: 8,
                bitsPerPixel: 32,
                bytesPerRow: bytesPerRow,
                space: colorSpace,
                bitmapInfo: CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue),
                provider: dataProvider,
                decode: nil,
                shouldInterpolate: false,
                intent: .defaultIntent
            )
        else {
            return nil
        }

        return compressedImage
    }

    /// Creates a structurally different image with noise pattern
    static func createStructurallyDifferentImage(
        width: Int, height: Int, color: (r: UInt8, g: UInt8, b: UInt8, a: UInt8)
    ) -> CGImage? {
        let bitsPerComponent = 8
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let colorSpace = CGColorSpaceCreateDeviceRGB()

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)

        // Create a random noise pattern with the base color
        for y in 0..<height {
            for x in 0..<width {
                let offset = (y * width + x) * bytesPerPixel

                // Generate random noise with base color as starting point
                let noiseR = Int(color.r) + Int.random(in: -50...50)
                let noiseG = Int(color.g) + Int.random(in: -50...50)
                let noiseB = Int(color.b) + Int.random(in: -50...50)

                pixelData[offset] = UInt8(max(0, min(255, noiseR)))
                pixelData[offset + 1] = UInt8(max(0, min(255, noiseG)))
                pixelData[offset + 2] = UInt8(max(0, min(255, noiseB)))
                pixelData[offset + 3] = color.a
            }
        }

        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.last.rawValue)
        guard let dataProvider = CGDataProvider(data: Data(pixelData) as CFData),
            let cgImage = CGImage(
                width: width,
                height: height,
                bitsPerComponent: bitsPerComponent,
                bitsPerPixel: bytesPerPixel * 8,
                bytesPerRow: bytesPerRow,
                space: colorSpace,
                bitmapInfo: bitmapInfo,
                provider: dataProvider,
                decode: nil,
                shouldInterpolate: false,
                intent: .defaultIntent
            )
        else {
            return nil
        }

        return cgImage
    }
}
