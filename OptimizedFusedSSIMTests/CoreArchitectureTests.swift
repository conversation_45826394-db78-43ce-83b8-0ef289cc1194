//
// CoreArchitectureTests.swift
// OptimizedFusedSSIMTests
//
// 功能描述：核心架构单元测试，验证硬件检测、纹理管理、异步引擎功能
// 优化阶段：第一阶段 - 核心架构重构
//

import XCTest
import Metal
import CoreGraphics
@testable import OptimizedFusedSSIM

class CoreArchitectureTests: XCTestCase {
    
    var device: MTLDevice!
    
    override func setUpWithError() throws {
        guard let systemDevice = MTLCreateSystemDefaultDevice() else {
            throw XCTSkip("Metal is not supported on this device")
        }
        device = systemDevice
    }
    
    override func tearDownWithError() throws {
        device = nil
    }
    
    // MARK: - 硬件检测测试
    
    func testHardwareDetection() throws {
        let profile = device.hardwareProfile
        
        // 验证基本硬件特性
        XCTAssertGreaterThan(profile.maxThreadsPerGroup, 0, "最大线程组大小应大于0")
        XCTAssertTrue([32, 64].contains(profile.simdWidth), "SIMD宽度应为32或64")
        XCTAssertGreaterThan(profile.recommendedOccupancy, 0, "推荐占用率应大于0")
        XCTAssertLessThanOrEqual(profile.recommendedOccupancy, 1.0, "推荐占用率应小于等于1.0")
        
        // 验证Apple GPU检测
        let isAppleDevice = device.name.contains("Apple")
        XCTAssertEqual(profile.isAppleGPU, isAppleDevice, "Apple GPU检测应正确")
        
        // 验证Half精度支持检测
        XCTAssertEqual(profile.supportsHalfPrecision, isAppleDevice, "Half精度支持检测应正确")
        
        print("硬件配置文件:")
        print(profile.description)
    }
    
    func testAdaptiveThreadConfig() throws {
        let testSizes = [
            CGSize(width: 512, height: 512),
            CGSize(width: 1024, height: 1024),
            CGSize(width: 2048, height: 2048),
            CGSize(width: 4096, height: 4096)
        ]

        for size in testSizes {
            let config = AdaptiveThreadConfig.optimal(for: device, imageSize: size)

            // 验证线程组大小不超过硬件限制
            let totalThreads = config.threadsPerThreadgroup.width * config.threadsPerThreadgroup.height
            let maxThreadsTotal = device.maxThreadsPerThreadgroup.width
            XCTAssertLessThanOrEqual(totalThreads, maxThreadsTotal,
                                   "线程组大小不应超过硬件限制 (图像尺寸: \(size))")

            // 验证线程组配置合理性
            XCTAssertGreaterThan(config.threadsPerThreadgroup.width, 0, "线程组宽度应大于0")
            XCTAssertGreaterThan(config.threadsPerThreadgroup.height, 0, "线程组高度应大于0")
            XCTAssertGreaterThan(config.threadgroupsPerGrid.width, 0, "网格宽度应大于0")
            XCTAssertGreaterThan(config.threadgroupsPerGrid.height, 0, "网格高度应大于0")

            print("图像尺寸 \(size) 的线程配置:")
            print(config.description)
        }
    }
    
    func testHardwareDetector() throws {
        let detector = HardwareDetector(device: device)

        // 测试硬件配置文件获取
        let profile = detector.hardwareProfile
        let maxThreadsTotal = device.maxThreadsPerThreadgroup.width
        XCTAssertEqual(profile.maxThreadsPerGroup, maxThreadsTotal)

        // 测试最优线程配置计算 - 使用基准尺寸
        let imageSize = CGSize(width: 1920, height: 1080)
        let config = detector.optimalThreadConfig(for: imageSize)
        XCTAssertGreaterThan(config.totalThreads, 0)

        // 测试支持的优化特性
        let optimizations = detector.supportedOptimizations()
        XCTAssertFalse(optimizations.isEmpty, "应该至少支持一种优化特性")

        // 测试性能调优建议
        let advice = detector.performanceTuningAdvice(for: imageSize)
        print("基准尺寸 (1920x1080) 性能调优建议: \(advice)")
        print("基准尺寸线程配置: \(config.description)")
    }
    
    // MARK: - 纹理管理测试
    
    func testTextureManagerInitialization() throws {
        let textureManager = try TextureManager(device: device)
        XCTAssertTrue(textureManager.isDeviceCompatible, "纹理管理器应与设备兼容")
    }
    
    func testZeroCopyTextureCreation() throws {
        let textureManager = try TextureManager(device: device)
        let testImage = try generateTestImage(size: CGSize(width: 512, height: 512))
        
        let texture = try textureManager.createTexture(from: testImage)
        
        // 验证纹理属性
        XCTAssertEqual(texture.width, 512, "纹理宽度应匹配")
        XCTAssertEqual(texture.height, 512, "纹理高度应匹配")
        XCTAssertEqual(texture.pixelFormat, .bgra8Unorm, "纹理格式应为BGRA8Unorm")
        
        print("纹理创建成功: \(texture.width)x\(texture.height)")
    }
    
    func testTextureCreationPerformance() throws {
        let textureManager = try TextureManager(device: device)
        let testImage = try generateTestImage(size: CGSize(width: 1920, height: 1080))

        // 测试性能
        let (texture, time) = try textureManager.createTextureWithPerformance(from: testImage)

        XCTAssertEqual(texture.width, 1920)
        XCTAssertEqual(texture.height, 1080)
        XCTAssertGreaterThan(time, 0, "创建时间应大于0")

        print("纹理创建耗时 (1920x1080): \(String(format: "%.2f", time))ms")
        print(textureManager.performanceStats)
    }
    
    func testTextureCaching() throws {
        let textureManager = try TextureManager(device: device)
        let testImage = try generateTestImage(size: CGSize(width: 256, height: 256))
        
        // 第一次创建
        let texture1 = try textureManager.createTexture(from: testImage)
        
        // 第二次创建（应该使用缓存）
        let texture2 = try textureManager.createTexture(from: testImage)
        
        // 验证纹理属性相同
        XCTAssertEqual(texture1.width, texture2.width)
        XCTAssertEqual(texture1.height, texture2.height)
        XCTAssertEqual(texture1.pixelFormat, texture2.pixelFormat)
        
        print("纹理缓存测试完成")
        print(textureManager.performanceStats)
    }
    
    // MARK: - MetalStateCache测试
    
    func testMetalStateCacheInitialization() throws {
        let stateCache = try MetalStateCache(device: device)
        
        // 验证基本属性
        XCTAssertEqual(stateCache.device.registryID, device.registryID)
        XCTAssertNotNil(stateCache.commandQueue)
        XCTAssertNotNil(stateCache.library)
        XCTAssertNotNil(stateCache.fusedSSIMPipelineState)
        XCTAssertNotNil(stateCache.reductionPipelineState)
        
        print("MetalStateCache 初始化成功")
        print(stateCache.deviceInfo)
    }
    
    func testDynamicPipelineStateCache() throws {
        let stateCache = try MetalStateCache(device: device)
        
        // 测试动态管道状态获取
        let descriptor = PipelineDescriptor(
            kernelName: "fusedSSIMKernel",
            threadgroupSize: MTLSize(width: 32, height: 32, depth: 1)
        )
        
        let pipelineState = try stateCache.getPipelineState(for: descriptor)
        XCTAssertNotNil(pipelineState)
        
        // 测试缓存命中
        let cachedPipelineState = try stateCache.getPipelineState(for: descriptor)
        XCTAssertEqual(pipelineState.label, cachedPipelineState.label)
        
        print("动态管道状态缓存测试完成")
        print(stateCache.cacheStats)
    }
    
    // MARK: - 异步执行引擎测试
    
    func testAsyncExecutionEngineInitialization() async throws {
        let engine = try SSIMCalculationEngine(device: device)
        
        // 验证引擎属性
        let hardwareInfo = await engine.hardwareInfo
        XCTAssertFalse(hardwareInfo.isEmpty, "硬件信息不应为空")
        
        let optimizations = await engine.supportedOptimizations
        XCTAssertFalse(optimizations.isEmpty, "支持的优化特性不应为空")
        
        print("异步执行引擎初始化成功")
        print("硬件信息: \(hardwareInfo)")
        print("支持的优化: \(optimizations)")
    }
    
    func testBasicSSIMCalculation() async throws {
        let engine = try SSIMCalculationEngine(device: device)

        let testImage = try generateTestImage(size: CGSize(width: 1920, height: 1080))

        // 测试相同图像的SSIM计算
        let identicalResult = try await engine.calculateSSIM(image1: testImage, image2: testImage)
        XCTAssertGreaterThan(identicalResult.ssim, 0.99, "相同图像的SSIM应接近1.0")

        // 测试不同图像的SSIM计算
        let differentImage = try generateDifferentTestImage(size: CGSize(width: 1920, height: 1080))
        let differentResult = try await engine.calculateSSIM(image1: testImage, image2: differentImage)
        XCTAssertLessThan(differentResult.ssim, 0.3, "不同图像的SSIM应小于0.3")

        print("基础SSIM计算测试完成 (1920x1080)")
        print("相同图像SSIM: \(identicalResult.ssim)")
        print("不同图像SSIM: \(differentResult.ssim)")

        let stats = await engine.performanceStats
        print("性能统计: \(stats)")
    }
    
    func testAsyncSSIMPerformance() async throws {
        let engine = try SSIMCalculationEngine(device: device)

        let testImage1 = try generateTestImage(size: CGSize(width: 1920, height: 1080))
        let testImage2 = try generateTestImage(size: CGSize(width: 1920, height: 1080))

        let (result, time) = try await engine.calculateSSIMWithPerformance(image1: testImage1, image2: testImage2)

        XCTAssertGreaterThan(result.ssim, 0.99)
        XCTAssertGreaterThan(time, 0)

        print("🚀 第一阶段性能基准测试 (1920x1080)")
        print("执行时间: \(String(format: "%.2f", time))ms")
        print("SSIM值: \(result.ssim)")
        print("像素数量: \(1920 * 1080) = 2,073,600 pixels")

        let stats = await engine.performanceStats
        print("详细性能统计: \(stats)")
    }
    
    // MARK: - 辅助方法
    
    private func generateTestImage(size: CGSize) throws -> CGImage {
        let width = Int(size.width)
        let height = Int(size.height)
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow
        
        var pixelData = [UInt8](repeating: 0, count: totalBytes)
        
        // 生成简单的渐变图案
        for y in 0..<height {
            for x in 0..<width {
                let offset = (y * width + x) * bytesPerPixel
                let intensity = UInt8((x + y) % 256)
                pixelData[offset] = intensity     // Blue
                pixelData[offset + 1] = intensity // Green
                pixelData[offset + 2] = intensity // Red
                pixelData[offset + 3] = 255       // Alpha
            }
        }
        
        guard let dataProvider = CGDataProvider(data: Data(pixelData) as CFData),
              let cgImage = CGImage(
                width: width,
                height: height,
                bitsPerComponent: 8,
                bitsPerPixel: 32,
                bytesPerRow: bytesPerRow,
                space: CGColorSpaceCreateDeviceRGB(),
                bitmapInfo: CGBitmapInfo(rawValue: CGImageAlphaInfo.noneSkipFirst.rawValue),
                provider: dataProvider,
                decode: nil,
                shouldInterpolate: false,
                intent: .defaultIntent
              ) else {
            throw XCTSkip("Failed to create test image")
        }
        
        return cgImage
    }
    
    private func generateDifferentTestImage(size: CGSize) throws -> CGImage {
        let width = Int(size.width)
        let height = Int(size.height)
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow

        var pixelData = [UInt8](repeating: 0, count: totalBytes)

        // 生成完全不同的图案 - 使用随机噪声和几何图案
        for y in 0..<height {
            for x in 0..<width {
                let offset = (y * width + x) * bytesPerPixel

                // 创建棋盘图案和随机噪声的组合
                let checkSize = 32
                let isCheckered = ((x / checkSize) + (y / checkSize)) % 2 == 0
                let baseIntensity: UInt8 = isCheckered ? 255 : 0

                // 添加随机噪声
                let noise = UInt8.random(in: 0...100)

                pixelData[offset] = baseIntensity     // Blue
                pixelData[offset + 1] = noise        // Green
                pixelData[offset + 2] = 255 - baseIntensity // Red
                pixelData[offset + 3] = 255          // Alpha
            }
        }

        guard let dataProvider = CGDataProvider(data: Data(pixelData) as CFData),
              let cgImage = CGImage(
                width: width,
                height: height,
                bitsPerComponent: 8,
                bitsPerPixel: 32,
                bytesPerRow: bytesPerRow,
                space: CGColorSpaceCreateDeviceRGB(),
                bitmapInfo: CGBitmapInfo(rawValue: CGImageAlphaInfo.noneSkipFirst.rawValue),
                provider: dataProvider,
                decode: nil,
                shouldInterpolate: false,
                intent: .defaultIntent
              ) else {
            throw XCTSkip("Failed to create different test image")
        }

        return cgImage
    }
}
