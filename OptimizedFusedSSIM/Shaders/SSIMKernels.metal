//
//  SSIMKernels.metal
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

#include <metal_stdlib>

using namespace metal;

// ------------------------------------------
// Image Preprocessing Parameters
// ------------------------------------------
struct ImageParams {
    uint width;
    uint height;
    uint channels;
};

// 简化的图像处理参数
struct ImageProcessParams {
    uint width;
    uint height;
    bool hasAlpha;
};

// ------------------------------------------
// Image Preprocessing Kernels (Phase 1)
// ------------------------------------------

/// GPU加速的图像解码和格式转换kernel
/// 处理从各种图像格式到统一RGBA8Unorm格式的转换
kernel void imageDecodeKernel(
    texture2d<half, access::read> sourceTexture [[texture(0)]],
    texture2d<half, access::write> targetTexture [[texture(1)]],
    constant ImageParams& params [[buffer(0)]],
    uint2 gid [[thread_position_in_grid]]
) {
    // 边界检查
    if (gid.x >= params.width || gid.y >= params.height) {
        return;
    }
    
    // 读取源像素
    half4 sourcePixel = sourceTexture.read(gid);
    
    // 基础像素格式转换（确保RGBA格式）
    half4 resultPixel;
    resultPixel.rgb = sourcePixel.rgb;
    resultPixel.a = (params.channels == 4) ? sourcePixel.a : 1.0h;
    
    // 写入目标纹理
    targetTexture.write(resultPixel, gid);
}

/// GPU加速的Alpha混合kernel
/// 实现高性能的Alpha通道处理和中性灰背景混合
kernel void alphaBlendKernel(
    texture2d<half, access::read> sourceTexture [[texture(0)]],
    texture2d<half, access::write> targetTexture [[texture(1)]],
    constant ImageParams& params [[buffer(0)]],
    uint2 gid [[thread_position_in_grid]]
) {
    // 边界检查
    if (gid.x >= params.width || gid.y >= params.height) {
        return;
    }
    
    // 读取源像素（预乘Alpha格式）
    half4 sourcePixel = sourceTexture.read(gid);
    
    if (params.channels != 4) {
        // 无Alpha通道，直接输出
        targetTexture.write(half4(sourcePixel.rgb, 1.0h), gid);
        return;
    }
    
    // 处理Alpha混合
    half alpha = sourcePixel.a;
    half3 premultipliedRGB = sourcePixel.rgb;
    
    // 反预乘处理（如果Alpha > 0）
    half3 originalRGB;
    if (alpha > 0.0h) {
        originalRGB = premultipliedRGB / alpha;
        // 限制到有效范围
        originalRGB = clamp(originalRGB, 0.0h, 1.0h);
    } else {
        originalRGB = half3(0.0h);
    }
    
    // 中性灰背景混合：C_rgb * C_a + 0.5 * (1 - C_a)
    half neutralGray = 0.5h;  // 中性灰 (0.5)
    half3 blendedRGB = originalRGB * alpha + neutralGray * (1.0h - alpha);
    
    // 输出最终像素（Alpha设为不透明）
    targetTexture.write(half4(blendedRGB, 1.0h), gid);
}

/// 简化的图像处理kernel - 统一处理有无Alpha的图像
/// 用于ImagePreProcessor的GPU加速路径
kernel void imagePreprocessKernel(
    texture2d<half, access::read> inputTexture [[texture(0)]],
    texture2d<half, access::write> outputTexture [[texture(1)]],
    constant ImageProcessParams& params [[buffer(0)]],
    uint2 gid [[thread_position_in_grid]]
) {
    // 边界检查
    if (gid.x >= params.width || gid.y >= params.height) {
        return;
    }
    
    // 读取源像素
    half4 sourcePixel = inputTexture.read(gid);
    
    if (!params.hasAlpha) {
        // 无Alpha通道的图像 - 直接输出，确保Alpha为不透明
        outputTexture.write(half4(sourcePixel.rgb, 1.0h), gid);
        return;
    }
    
    // 有Alpha通道的图像 - 进行Alpha混合处理
    half alpha = sourcePixel.a;
    half3 premultipliedRGB = sourcePixel.rgb;
    
    // 中性灰背景
    half neutralGray = 0.5h;
    
    // 反预乘并混合
    half3 finalRGB;
    if (alpha > 0.001h) {
        half3 originalRGB = clamp(premultipliedRGB / alpha, 0.0h, 1.0h);
        finalRGB = originalRGB * alpha + neutralGray * (1.0h - alpha);
    } else {
        finalRGB = half3(neutralGray);
    }
    
    // 输出最终像素（Alpha设为不透明）
    outputTexture.write(half4(finalRGB, 1.0h), gid);
}

// ------------------------------------------
// Block and Shared Memory Dimensions
// ------------------------------------------
#define BLOCK_X 16
#define BLOCK_Y 16
#define HALO    5

#define SHARED_X (BLOCK_X + 2 * HALO)
#define SHARED_Y (BLOCK_Y + 2 * HALO)

// For partial results after horizontal pass
#define CONV_X BLOCK_X
#define CONV_Y SHARED_Y

// ------------------------------------------
// Constant Memory for Gaussian Coefficients
// ------------------------------------------
constant float cGauss[11] = {
    0.001028380123898387f,
    0.0075987582094967365f,
    0.036000773310661316f,
    0.10936068743467331f,
    0.21300552785396576f,
    0.26601171493530273f,
    0.21300552785396576f,
    0.10936068743467331f,
    0.036000773310661316f,
    0.0075987582094967365f,
    0.001028380123898387f
};

// ------------------------------------------
// Utility: Channel value extraction
// ------------------------------------------
// Extracts a specific channel from a half4 vector and converts it to float.
// The texture is rgba8Unorm, which Metal reads as `half`.
static inline float get_channel_value(half4 p, uint channel_index) {
    return float(p[channel_index]);
}

// ------------------------------------------
// Unified Fused SSIM Kernel
// ------------------------------------------
kernel void fusedSSIMKernel(
    texture2d<half, access::read> image1 [[texture(0)]],
    texture2d<half, access::read> image2 [[texture(1)]],
    device float* ssim_map [[buffer(0)]],
    constant float &C1 [[buffer(1)]],
    constant float &C2 [[buffer(2)]],
    constant uint &channel_index [[buffer(3)]],
    uint2 tg_pos [[threadgroup_position_in_grid]],
    uint2 tt_pos [[thread_position_in_threadgroup]],
    uint2 tg_size [[threads_per_threadgroup]]
) {
    const uint W = image1.get_width();
    const uint H = image1.get_height();

    threadgroup float sTile[SHARED_Y][SHARED_X][2];
    threadgroup float xconv[CONV_Y][CONV_X][5];

    // 1. Load (img1, img2) tile + halo into shared memory
    {
        const int tileStartY = tg_pos.y * BLOCK_Y;
        const int tileStartX = tg_pos.x * BLOCK_X;
        const int thread_rank = tt_pos.y * tg_size.x + tt_pos.x;

        for (int i = thread_rank; i < SHARED_X * SHARED_Y; i += tg_size.x * tg_size.y) {
            int local_y = i / SHARED_X;
            int local_x = i % SHARED_X;
            int gy = tileStartY + local_y - HALO;
            int gx = tileStartX + local_x - HALO;

            float X, Y;
            if (gx < 0 || gx >= int(W) || gy < 0 || gy >= int(H)) {
                // Zero padding for out-of-bounds pixels
                X = 0.0f;
                Y = 0.0f;
            } else {
                // Read as half4 and extract the relevant channel
                half4 p1 = image1.read(uint2(gx, gy));
                half4 p2 = image2.read(uint2(gx, gy));
                X = get_channel_value(p1, channel_index);
                Y = get_channel_value(p2, channel_index);
            }

            sTile[local_y][local_x][0] = X;
            sTile[local_y][local_x][1] = Y;
        }
    }
    threadgroup_barrier(mem_flags::mem_threadgroup);

    // 2. Horizontal convolution (11x1) in shared memory
    {
        int ly = tt_pos.y;
        int lx = tt_pos.x + HALO;

        float sumX = 0.f, sumX2 = 0.f, sumY = 0.f, sumY2 = 0.f, sumXY = 0.f;

        #pragma unroll
        for (int d = 1; d <= HALO; ++d) {
            float w = cGauss[HALO - d];
            float Xleft  = sTile[ly][lx - d][0];
            float Yleft  = sTile[ly][lx - d][1];
            float Xright = sTile[ly][lx + d][0];
            float Yright = sTile[ly][lx + d][1];

            sumX  += (Xleft + Xright) * w;
            sumX2 += ((Xleft * Xleft) + (Xright * Xright)) * w;
            sumY  += (Yleft + Yright) * w;
            sumY2 += ((Yleft * Yleft) + (Yright * Yright)) * w;
            sumXY += ((Xleft * Yleft) + (Xright * Yright)) * w;
        }
        // center
        float centerX = sTile[ly][lx][0];
        float centerY = sTile[ly][lx][1];
        float wc = cGauss[HALO];
        sumX  += centerX * wc;
        sumX2 += (centerX * centerX) * wc;
        sumY  += centerY * wc;
        sumY2 += (centerY * centerY) * wc;
        sumXY += (centerX * centerY) * wc;

        // Write out partial sums
        xconv[ly][tt_pos.x][0] = sumX;
        xconv[ly][tt_pos.x][1] = sumX2;
        xconv[ly][tt_pos.x][2] = sumY;
        xconv[ly][tt_pos.x][3] = sumY2;
        xconv[ly][tt_pos.x][4] = sumXY;

        // Possibly handle second row in same warp
        int ly2 = ly + BLOCK_Y;
        if (ly2 < CONV_Y) {
            sumX = 0.f; sumX2 = 0.f;
            sumY = 0.f; sumY2 = 0.f;
            sumXY = 0.f;

            #pragma unroll
            for (int d = 1; d <= HALO; ++d) {
                float w = cGauss[HALO - d];
                float Xleft  = sTile[ly2][lx - d][0];
                float Yleft  = sTile[ly2][lx - d][1];
                float Xright = sTile[ly2][lx + d][0];
                float Yright = sTile[ly2][lx + d][1];

                sumX  += (Xleft + Xright) * w;
                sumX2 += ((Xleft * Xleft) + (Xright * Xright)) * w;
                sumY  += (Yleft + Yright) * w;
                sumY2 += ((Yleft * Yleft) + (Yright * Yright)) * w;
                sumXY += ((Xleft * Yleft) + (Xright * Yright)) * w;
            }
            // center
            float cx = sTile[ly2][lx][0];
            float cy = sTile[ly2][lx][1];
            wc = cGauss[HALO];
            sumX  += cx * wc;
            sumX2 += (cx * cx) * wc;
            sumY  += cy * wc;
            sumY2 += (cy * cy) * wc;
            sumXY += (cx * cy) * wc;

            xconv[ly2][tt_pos.x][0] = sumX;
            xconv[ly2][tt_pos.x][1] = sumX2;
            xconv[ly2][tt_pos.x][2] = sumY;
            xconv[ly2][tt_pos.x][3] = sumY2;
            xconv[ly2][tt_pos.x][4] = sumXY;
        }
    }
    threadgroup_barrier(mem_flags::mem_threadgroup);

    // 3. Vertical convolution (1x11) + final SSIM
    {
        int ly = tt_pos.y + HALO;
        int lx = tt_pos.x;

        float out0 = 0.f, out1 = 0.f, out2 = 0.f, out3 = 0.f, out4 = 0.f;

        #pragma unroll
        for (int d = 1; d <= HALO; ++d) {
            float w = cGauss[HALO - d];
            threadgroup float* top = xconv[ly - d][lx];
            threadgroup float* bot = xconv[ly + d][lx];

            out0 += (top[0] + bot[0]) * w;
            out1 += (top[1] + bot[1]) * w;
            out2 += (top[2] + bot[2]) * w;
            out3 += (top[3] + bot[3]) * w;
            out4 += (top[4] + bot[4]) * w;
        }
        // center
        float wC = cGauss[HALO];
        threadgroup float* ctr = xconv[ly][lx];
        out0 += ctr[0] * wC;
        out1 += ctr[1] * wC;
        out2 += ctr[2] * wC;
        out3 += ctr[3] * wC;
        out4 += ctr[4] * wC;

        const uint pix_x = tg_pos.x * BLOCK_X + tt_pos.x;
        const uint pix_y = tg_pos.y * BLOCK_Y + tt_pos.y;
        const uint pix_id = pix_y * W + pix_x;

        if (pix_x < W && pix_y < H) {
            float mu1 = out0;
            float mu2 = out2;
            float mu1_sq = mu1 * mu1;
            float mu2_sq = mu2 * mu2;

            float sigma1_sq = out1 - mu1_sq;
            float sigma2_sq = out3 - mu2_sq;
            float sigma12   = out4 - mu1 * mu2;

            float A = mu1_sq + mu2_sq + C1;
            float B = sigma1_sq + sigma2_sq + C2;
            float C_ = 2.f * mu1 * mu2 + C1;
            float D_ = 2.f * sigma12 + C2;

            float val = (C_ * D_) / (A * B);
            // Clamp SSIM value to valid range [0, 1] to handle floating-point precision errors
            val = clamp(val, 0.0f, 1.0f);
            ssim_map[pix_id] = val;
        }
    }
}


// ------------------------------------------
// Reduction Average Kernel
// ------------------------------------------
kernel void reductionAverageKernel(
    device const float* ssim_map [[buffer(0)]],
    device float* result_buffer [[buffer(1)]],
    constant uint& element_count [[buffer(2)]],
    constant uint& num_threadgroups [[buffer(3)]],  // 新增：线程组数量
    uint tid_in_tg [[thread_index_in_threadgroup]],
    uint tg_size [[threads_per_threadgroup]],
    uint gid [[thread_position_in_grid]],
    uint tg_id [[threadgroup_position_in_grid]]
) {
    threadgroup float shared_mem[BLOCK_X * BLOCK_Y];

    // Each thread loads one element from global memory to shared memory
    // FIXED: Use passed num_threadgroups to calculate total threads
    uint total_threads = num_threadgroups * tg_size;
    float sum = 0.0f;
    for (uint i = gid; i < element_count; i += total_threads) {
        sum += ssim_map[i];
    }
    shared_mem[tid_in_tg] = sum;

    threadgroup_barrier(mem_flags::mem_threadgroup);

    // Reduction within threadgroup
    for (uint s = tg_size >> 1; s > 0; s >>= 1) {
        if (tid_in_tg < s) {
            shared_mem[tid_in_tg] += shared_mem[tid_in_tg + s];
        }
        threadgroup_barrier(mem_flags::mem_threadgroup);
    }

    // Write the result of this threadgroup to global memory
    if (tid_in_tg == 0) {
        result_buffer[tg_id] = shared_mem[0];
    }
}
