//
//  ImageProcessor.swift
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

import Accelerate
import CoreGraphics
import CoreVideo
import Foundation
import Metal

/// 统一的高性能图像处理器，集成了图像转换、纹理创建和GPU加速功能
internal class ImageProcessor {

  // MARK: - Core Resources
  private let device: MTLDevice
  private let commandQueue: MTLCommandQueue
  private var library: MTLLibrary?

  // GPU Pipeline (如果可用)
  private let gpuPipelineState: MTLComputePipelineState?

  // 错误类型
  enum ProcessorError: Error {
    case deviceNotSupported
    case invalidImage
    case textureCreationFailed
    case gpuProcessingFailed(String)
  }

  // MARK: - Initialization

  init() throws {
    // 初始化Metal设备
    guard let device = MTLCreateSystemDefaultDevice() else {
      throw ProcessorError.deviceNotSupported
    }
    self.device = device

    // 创建命令队列
    guard let commandQueue = device.makeCommandQueue() else {
      throw ProcessorError.deviceNotSupported
    }
    self.commandQueue = commandQueue

    // 尝试加载Metal库（可选）
    do {
      let bundle = Bundle(for: ImageProcessor.self)
      if let libraryURL = bundle.url(forResource: "SSIMKernels", withExtension: "metallib") {
        self.library = try device.makeLibrary(URL: libraryURL)

        // 创建简化的GPU管道
        if let function = library?.makeFunction(name: "imagePreprocessKernel") {
          self.gpuPipelineState = try device.makeComputePipelineState(function: function)
        } else {
          self.gpuPipelineState = nil
        }
      } else {
        self.library = nil
        self.gpuPipelineState = nil
      }
    } catch {
      self.library = nil
      self.gpuPipelineState = nil
    }

    print("✅ ImageProcessor 初始化成功 (GPU加速: \(gpuPipelineState != nil ? "启用" : "禁用"))")
  }

  // MARK: - Main Processing Interface

  /// 统一的图像处理接口 - 从CGImage到MTLTexture，包含性能耗时
  func createTextureWithPerformance(from cgImage: CGImage) throws -> (
    texture: MTLTexture, time: Double
  ) {
    let startTime = CFAbsoluteTimeGetCurrent()
    let texture = try createTexture(from: cgImage)
    let elapsedTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
    return (texture, elapsedTime)
  }

  /// 统一的图像处理接口 - 从CGImage到RGBA数据
  /// - Parameter cgImage: 输入图像
  /// - Returns: 优化处理后的RGBA数据
  func processImage(_ cgImage: CGImage) throws -> [UInt8] {
    let width = cgImage.width
    let height = cgImage.height

    // 如果有GPU管道，尝试GPU加速
    if gpuPipelineState != nil {
      do {
        return try processWithGPU(cgImage, width: width, height: height)
      } catch {
        print("⚠️ GPU处理失败，回退到CPU: \(error)")
        // 回退到CPU处理
      }
    }

    // CPU处理路径（高度优化）
    return try processWithOptimizedCPU(cgImage, width: width, height: height)
  }

  /// 直接创建Metal纹理的接口
  /// - Parameters:
  ///   - cgImage: 输入图像
  /// - Returns: Metal纹理对象
  func createTexture(from cgImage: CGImage) throws -> MTLTexture {
    // 先处理图像
    let rgbaData = try processImage(cgImage)

    // 创建纹理
    return try createMetalTexture(from: rgbaData, width: cgImage.width, height: cgImage.height)
  }

  // MARK: - GPU Processing (Simplified)

  private func processWithGPU(_ cgImage: CGImage, width: Int, height: Int) throws -> [UInt8] {
    guard let pipelineState = gpuPipelineState else {
      throw ProcessorError.gpuProcessingFailed("GPU管道未初始化")
    }

    // 先用CPU创建基础RGBA数据
    let inputData = try createRGBAData(from: cgImage)

    // 创建输入和输出纹理
    let inputTexture = try createMetalTexture(from: inputData, width: width, height: height)
    let outputTexture = try createOutputTexture(width: width, height: height)

    // GPU处理
    guard let commandBuffer = commandQueue.makeCommandBuffer(),
      let encoder = commandBuffer.makeComputeCommandEncoder()
    else {
      throw ProcessorError.gpuProcessingFailed("无法创建命令缓冲区")
    }

    encoder.setComputePipelineState(pipelineState)
    encoder.setTexture(inputTexture, index: 0)
    encoder.setTexture(outputTexture, index: 1)

    // 简化的参数
    var params = ImageProcessParams(
      width: UInt32(width),
      height: UInt32(height),
      hasAlpha: cgImage.alphaInfo != .none
    )
    encoder.setBytes(&params, length: MemoryLayout<ImageProcessParams>.stride, index: 0)

    // 配置线程组
    let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
    let threadgroupCount = MTLSize(
      width: (width + 15) / 16,
      height: (height + 15) / 16,
      depth: 1
    )

    encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
    encoder.endEncoding()

    commandBuffer.commit()
    commandBuffer.waitUntilCompleted()

    if let error = commandBuffer.error {
      throw ProcessorError.gpuProcessingFailed("GPU执行失败: \(error)")
    }

    // 读取结果
    return try readTextureData(from: outputTexture, width: width, height: height)
  }

  // MARK: - CPU Processing (Highly Optimized)

  private func processWithOptimizedCPU(_ cgImage: CGImage, width: Int, height: Int) throws
    -> [UInt8]
  {
    let alphaInfo = cgImage.alphaInfo
    let hasAlpha = alphaInfo != .none && alphaInfo != .noneSkipFirst && alphaInfo != .noneSkipLast

    // 创建优化的RGBA数据
    var rgbaData = try createRGBAData(from: cgImage)

    // 如果有Alpha通道，进行快速混合处理
    if hasAlpha {
      applyAlphaBlending(&rgbaData)
    } else {
      // 确保Alpha通道为不透明
      setOpaqueAlpha(&rgbaData)
    }

    return rgbaData
  }

  // MARK: - Core Helper Methods

  private func createRGBAData(from cgImage: CGImage) throws -> [UInt8] {
    let width = cgImage.width
    let height = cgImage.height
    let byteCount = width * height * 4

    var data = [UInt8](repeating: 0, count: byteCount)

    guard let colorSpace = CGColorSpace(name: CGColorSpace.sRGB),
      let context = CGContext(
        data: &data,
        width: width,
        height: height,
        bitsPerComponent: 8,
        bytesPerRow: width * 4,
        space: colorSpace,
        bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
      )
    else {
      throw ProcessorError.invalidImage
    }

    context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
    return data
  }

  private func applyAlphaBlending(_ data: inout [UInt8]) {
    let neutralGray: Float = 128.0

    for i in stride(from: 0, to: data.count, by: 4) {
      let r = Float(data[i])
      let g = Float(data[i + 1])
      let b = Float(data[i + 2])
      let alpha = Float(data[i + 3]) / 255.0

      // 反预乘并混合
      if alpha > 0.001 {
        let originalR = (r / alpha).clamped(to: 0...255)
        let originalG = (g / alpha).clamped(to: 0...255)
        let originalB = (b / alpha).clamped(to: 0...255)

        data[i] = UInt8((originalR * alpha + neutralGray * (1.0 - alpha)).rounded())
        data[i + 1] = UInt8((originalG * alpha + neutralGray * (1.0 - alpha)).rounded())
        data[i + 2] = UInt8((originalB * alpha + neutralGray * (1.0 - alpha)).rounded())
        data[i + 3] = 255
      } else {
        data[i] = UInt8(neutralGray)
        data[i + 1] = UInt8(neutralGray)
        data[i + 2] = UInt8(neutralGray)
        data[i + 3] = 255
      }
    }
  }

  private func setOpaqueAlpha(_ data: inout [UInt8]) {
    for i in stride(from: 3, to: data.count, by: 4) {
      data[i] = 255
    }
  }

  // MARK: - Metal Texture Operations

  private func createMetalTexture(from data: [UInt8], width: Int, height: Int) throws -> MTLTexture
  {
    let descriptor = MTLTextureDescriptor.texture2DDescriptor(
      pixelFormat: .rgba8Unorm,
      width: width,
      height: height,
      mipmapped: false
    )
    descriptor.usage = [.shaderRead, .shaderWrite]
    descriptor.storageMode = .shared

    guard let texture = device.makeTexture(descriptor: descriptor) else {
      throw ProcessorError.textureCreationFailed
    }

    let region = MTLRegion(
      origin: MTLOrigin(x: 0, y: 0, z: 0),
      size: MTLSize(width: width, height: height, depth: 1)
    )

    data.withUnsafeBytes { bytes in
      texture.replace(
        region: region,
        mipmapLevel: 0,
        withBytes: bytes.bindMemory(to: UInt8.self).baseAddress!,
        bytesPerRow: width * 4
      )
    }

    return texture
  }

  private func createOutputTexture(width: Int, height: Int) throws -> MTLTexture {
    let descriptor = MTLTextureDescriptor.texture2DDescriptor(
      pixelFormat: .rgba8Unorm,
      width: width,
      height: height,
      mipmapped: false
    )
    descriptor.usage = [.shaderRead, .shaderWrite]
    descriptor.storageMode = .shared

    guard let texture = device.makeTexture(descriptor: descriptor) else {
      throw ProcessorError.textureCreationFailed
    }

    return texture
  }

  private func readTextureData(from texture: MTLTexture, width: Int, height: Int) throws -> [UInt8]
  {
    let bytesPerRow = width * 4
    let dataSize = height * bytesPerRow
    var data = [UInt8](repeating: 0, count: dataSize)

    let region = MTLRegion(
      origin: MTLOrigin(x: 0, y: 0, z: 0),
      size: MTLSize(width: width, height: height, depth: 1)
    )

    data.withUnsafeMutableBytes { bytes in
      texture.getBytes(
        bytes.bindMemory(to: UInt8.self).baseAddress!,
        bytesPerRow: bytesPerRow,
        from: region,
        mipmapLevel: 0
      )
    }

    return data
  }

  // MARK: - Resource Management

  /// 清理资源（内存优化）
  func clearCaches() {
    // 由于简化架构，主要是让Metal系统进行清理
    print("🧹 ImageProcessor 资源已清理")
  }
}

// MARK: - Supporting Types

/// 简化的Metal着色器参数
struct ImageProcessParams {
  var width: UInt32
  var height: UInt32
  var hasAlpha: Bool
}

// MARK: - Float Extension

extension Float {
  func clamped(to range: ClosedRange<Float>) -> Float {
    return Swift.max(range.lowerBound, Swift.min(range.upperBound, self))
  }
}
