//
// AsyncExecutionEngine.swift
// OptimizedFusedSSIM
//
// 功能描述：异步执行引擎，基于Swift Actor实现非阻塞SSIM计算
// 优化阶段：第一阶段 - 核心架构重构
//

@preconcurrency import Metal
import CoreGraphics
import Foundation

// MARK: - SSIM计算引擎

/// 基于Swift Actor的异步SSIM计算引擎
actor SSIMCalculationEngine {
    private let device: MTLDevice
    private let textureManager: TextureManager
    private let metalKernel: MetalSSIMKernel
    private let hardwareDetector: HardwareDetector

    // 性能统计
    private var calculationCount = 0
    private var totalExecutionTime: Double = 0

    /// 初始化异步计算引擎
    /// - Parameter device: Metal设备
    /// - Throws: 初始化失败时抛出错误
    init(device: MTLDevice) throws {
        self.device = device

        self.textureManager = try TextureManager(device: device)
        self.metalKernel = try MetalSSIMKernel()
        self.hardwareDetector = HardwareDetector(device: device)

        print("✅ SSIMCalculationEngine 初始化成功 (设备: \(device.name))")
    }
    
    // MARK: - 公共异步接口
    
    /// 异步计算两张图像的SSIM值
    /// - Parameters:
    ///   - image1: 第一张图像
    ///   - image2: 第二张图像
    /// - Returns: SSIM计算结果
    /// - Throws: 计算过程中的错误
    func calculateSSIM(image1: CGImage, image2: CGImage) async throws -> SSIMResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 验证图像尺寸
        guard image1.width == image2.width && image1.height == image2.height else {
            throw SSIMError.imageSizeMismatch
        }
        
        // 异步创建纹理
        async let texture1Task = textureManager.createTexture(from: image1)
        async let texture2Task = textureManager.createTexture(from: image2)
        
        let (texture1, texture2) = try await (texture1Task, texture2Task)
        
        // 异步执行计算
        let ssimValue = try await executeComputation(texture1: texture1, texture2: texture2)
        
        // 更新统计信息
        calculationCount += 1
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        totalExecutionTime += executionTime
        
        return SSIMResult(
            ssim: Double(ssimValue),
            imageSize: ImageSize(width: image1.width, height: image1.height)
        )
    }
    
    /// 异步计算SSIM值并返回性能报告
    /// - Parameters:
    ///   - image1: 第一张图像
    ///   - image2: 第二张图像
    /// - Returns: SSIM结果和性能报告的元组
    /// - Throws: 计算过程中的错误
    func calculateSSIMWithPerformance(image1: CGImage, image2: CGImage) async throws -> (result: SSIMResult, time: Double) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await calculateSSIM(image1: image1, image2: image2)
        let executionTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
        return (result, executionTime)
    }
    
    /// 获取性能统计信息
    var performanceStats: String {
        let avgTime = calculationCount > 0 ? totalExecutionTime / Double(calculationCount) * 1000 : 0
        return """
        计算次数: \(calculationCount)
        总执行时间: \(String(format: "%.2f", totalExecutionTime * 1000))ms
        平均执行时间: \(String(format: "%.2f", avgTime))ms
        硬件信息: \(device.name)
        """
    }
    
    /// 清理缓存和资源
    func clearCaches() {
        textureManager.clearCaches()
        print("🧹 SSIMCalculationEngine 缓存已清理")
    }
    
    // MARK: - 私有计算实现
    
    /// 执行异步GPU计算
    private func executeComputation(texture1: MTLTexture, texture2: MTLTexture) async throws -> Float {
        // 直接使用现有的MetalSSIMKernel进行同步计算
        // 在Actor内部，这已经是异步的了
        let ssimValue = try metalKernel.calculate(from: texture1, to: texture2)
        return Float(ssimValue)
    }
}

// MARK: - 扩展功能

extension SSIMCalculationEngine {
    /// 获取硬件信息
    var hardwareInfo: String {
        return hardwareDetector.hardwareProfile.description
    }
    
    /// 获取支持的优化特性
    var supportedOptimizations: [String] {
        return hardwareDetector.supportedOptimizations()
    }
}
