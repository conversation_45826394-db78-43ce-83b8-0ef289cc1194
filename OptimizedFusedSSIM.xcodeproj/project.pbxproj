// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		23CBA4132E1D568E00EF9BD7 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23CBA4122E1D568E00EF9BD7 /* VideoToolbox.framework */; };
		23CBA4152E1D569500EF9BD7 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23CBA4142E1D569500EF9BD7 /* QuartzCore.framework */; };
		23E421B52E1A744B0096F893 /* OptimizedFusedSSIM.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23E421AB2E1A744B0096F893 /* OptimizedFusedSSIM.framework */; };
		23E421F62E1ACEE30096F893 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23E421F52E1ACEE30096F893 /* Metal.framework */; };
		23E421F82E1ACEED0096F893 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23E421F72E1ACEED0096F893 /* CoreGraphics.framework */; };
		23E421FA2E1ACEF60096F893 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23E421F92E1ACEF60096F893 /* Accelerate.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		23E421B62E1A744B0096F893 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 23E421A22E1A744B0096F893 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 23E421AA2E1A744B0096F893;
			remoteInfo = OptimizedFusedSSIM;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		23CBA4122E1D568E00EF9BD7 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		23CBA4142E1D569500EF9BD7 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		23E421AB2E1A744B0096F893 /* OptimizedFusedSSIM.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = OptimizedFusedSSIM.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		23E421B42E1A744B0096F893 /* OptimizedFusedSSIMTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = OptimizedFusedSSIMTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		23E421F52E1ACEE30096F893 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		23E421F72E1ACEED0096F893 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		23E421F92E1ACEF60096F893 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		23CBA3A52E1D25B900EF9BD7 /* Exceptions for "OptimizedFusedSSIM" folder in "OptimizedFusedSSIM" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Shaders/SSIMKernels.metal,
			);
			target = 23E421AA2E1A744B0096F893 /* OptimizedFusedSSIM */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		23E421AD2E1A744B0096F893 /* OptimizedFusedSSIM */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				23CBA3A52E1D25B900EF9BD7 /* Exceptions for "OptimizedFusedSSIM" folder in "OptimizedFusedSSIM" target */,
			);
			path = OptimizedFusedSSIM;
			sourceTree = "<group>";
		};
		23E421B82E1A744B0096F893 /* OptimizedFusedSSIMTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = OptimizedFusedSSIMTests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		23E421A82E1A744B0096F893 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				23CBA4152E1D569500EF9BD7 /* QuartzCore.framework in Frameworks */,
				23CBA4132E1D568E00EF9BD7 /* VideoToolbox.framework in Frameworks */,
				23E421FA2E1ACEF60096F893 /* Accelerate.framework in Frameworks */,
				23E421F82E1ACEED0096F893 /* CoreGraphics.framework in Frameworks */,
				23E421F62E1ACEE30096F893 /* Metal.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		23E421B12E1A744B0096F893 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				23E421B52E1A744B0096F893 /* OptimizedFusedSSIM.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		23E421A12E1A744B0096F893 = {
			isa = PBXGroup;
			children = (
				23E421AD2E1A744B0096F893 /* OptimizedFusedSSIM */,
				23E421B82E1A744B0096F893 /* OptimizedFusedSSIMTests */,
				23E421F42E1ACEE30096F893 /* Frameworks */,
				23E421AC2E1A744B0096F893 /* Products */,
			);
			sourceTree = "<group>";
		};
		23E421AC2E1A744B0096F893 /* Products */ = {
			isa = PBXGroup;
			children = (
				23E421AB2E1A744B0096F893 /* OptimizedFusedSSIM.framework */,
				23E421B42E1A744B0096F893 /* OptimizedFusedSSIMTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		23E421F42E1ACEE30096F893 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				23CBA4142E1D569500EF9BD7 /* QuartzCore.framework */,
				23CBA4122E1D568E00EF9BD7 /* VideoToolbox.framework */,
				23E421F92E1ACEF60096F893 /* Accelerate.framework */,
				23E421F72E1ACEED0096F893 /* CoreGraphics.framework */,
				23E421F52E1ACEE30096F893 /* Metal.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		23E421A62E1A744B0096F893 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		23E421AA2E1A744B0096F893 /* OptimizedFusedSSIM */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 23E421BD2E1A744B0096F893 /* Build configuration list for PBXNativeTarget "OptimizedFusedSSIM" */;
			buildPhases = (
				23E421A62E1A744B0096F893 /* Headers */,
				23CBA39D2E1D236900EF9BD7 /* ShellScript */,
				23E421A72E1A744B0096F893 /* Sources */,
				23E421A82E1A744B0096F893 /* Frameworks */,
				23E421A92E1A744B0096F893 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				23E421AD2E1A744B0096F893 /* OptimizedFusedSSIM */,
			);
			name = OptimizedFusedSSIM;
			packageProductDependencies = (
			);
			productName = OptimizedFusedSSIM;
			productReference = 23E421AB2E1A744B0096F893 /* OptimizedFusedSSIM.framework */;
			productType = "com.apple.product-type.framework";
		};
		23E421B32E1A744B0096F893 /* OptimizedFusedSSIMTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 23E421C02E1A744B0096F893 /* Build configuration list for PBXNativeTarget "OptimizedFusedSSIMTests" */;
			buildPhases = (
				23E421B02E1A744B0096F893 /* Sources */,
				23E421B12E1A744B0096F893 /* Frameworks */,
				23E421B22E1A744B0096F893 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				23E421B72E1A744B0096F893 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				23E421B82E1A744B0096F893 /* OptimizedFusedSSIMTests */,
			);
			name = OptimizedFusedSSIMTests;
			packageProductDependencies = (
			);
			productName = OptimizedFusedSSIMTests;
			productReference = 23E421B42E1A744B0096F893 /* OptimizedFusedSSIMTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		23E421A22E1A744B0096F893 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					23E421AA2E1A744B0096F893 = {
						CreatedOnToolsVersion = 16.4;
					};
					23E421B32E1A744B0096F893 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = 23E421A52E1A744B0096F893 /* Build configuration list for PBXProject "OptimizedFusedSSIM" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 23E421A12E1A744B0096F893;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 23E421AC2E1A744B0096F893 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				23E421AA2E1A744B0096F893 /* OptimizedFusedSSIM */,
				23E421B32E1A744B0096F893 /* OptimizedFusedSSIMTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		23E421A92E1A744B0096F893 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		23E421B22E1A744B0096F893 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		23CBA39D2E1D236900EF9BD7 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
				"$(PROJECT_DIR)/OptimizedFusedSSIM/Resources/SSIMKernels.metallib",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Define paths relative to the project root ($SRCROOT)\nINPUT_FILE=\"${SRCROOT}/OptimizedFusedSSIM/Shaders/SSIMKernels.metal\"\nOUTPUT_DIR=\"${SRCROOT}/OptimizedFusedSSIM/Resources\"\n\n# Create the output directory if it doesn't exist, just in case.\nmkdir -p \"${OUTPUT_DIR}\"\n\n# Define intermediate and final output paths\nAIR_FILE=\"${OUTPUT_DIR}/SSIMKernels.air\"\nMETALLIB_FILE=\"${OUTPUT_DIR}/SSIMKernels.metallib\"\n\n# Clean up previous build artifacts\nrm -f \"${AIR_FILE}\"\nrm -f \"${METALLIB_FILE}\"\n\n# Compile .metal to .air, providing the full input path\nxcrun metal -c \"${INPUT_FILE}\" -o \"${AIR_FILE}\"\n\n# Link .air to .metallib, providing full paths\nxcrun metallib \"${AIR_FILE}\" -o \"${METALLIB_FILE}\"\n\n# Clean up intermediate file\nrm -f \"${AIR_FILE}\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		23E421A72E1A744B0096F893 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		23E421B02E1A744B0096F893 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		23E421B72E1A744B0096F893 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 23E421AA2E1A744B0096F893 /* OptimizedFusedSSIM */;
			targetProxy = 23E421B62E1A744B0096F893 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		23E421BB2E1A744B0096F893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		23E421BC2E1A744B0096F893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		23E421BE2E1A744B0096F893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.ueworks.OptimizedFusedSSIM;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_MODULE = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		23E421BF2E1A744B0096F893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.ueworks.OptimizedFusedSSIM;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_MODULE = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		23E421C12E1A744B0096F893 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ueworks.OptimizedFusedSSIMTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		23E421C22E1A744B0096F893 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ueworks.OptimizedFusedSSIMTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		23E421A52E1A744B0096F893 /* Build configuration list for PBXProject "OptimizedFusedSSIM" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				23E421BB2E1A744B0096F893 /* Debug */,
				23E421BC2E1A744B0096F893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		23E421BD2E1A744B0096F893 /* Build configuration list for PBXNativeTarget "OptimizedFusedSSIM" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				23E421BE2E1A744B0096F893 /* Debug */,
				23E421BF2E1A744B0096F893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		23E421C02E1A744B0096F893 /* Build configuration list for PBXNativeTarget "OptimizedFusedSSIMTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				23E421C12E1A744B0096F893 /* Debug */,
				23E421C22E1A744B0096F893 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 23E421A22E1A744B0096F893 /* Project object */;
}
