# OptimizedFusedSSIM Metal 架构深度优化方案 v2.0

## 执行摘要

经过深入分析当前 Metal 实现，发现核心性能瓶颈集中在**线程调度策略**、**数据流转效率**和**GPU 资源利用率**三个方面。当前实现存在固定线程配置、过度串行化和资源重复创建等问题，严重限制了在 Apple Silicon 上的性能表现。

本优化方案基于 Metal 框架特性和 Apple GPU 架构特点，提出了**自适应线程调度**、**零拷贝数据流**和**统一计算管线**的优化策略，预期可实现 **20-30 倍**的性能提升。

## 1. 核心问题深度分析

### 1.1 线程调度问题（最严重）

**现状分析**：

- 当前实现固定使用 16x16 线程组配置（`BLOCK_X=16, BLOCK_Y=16`）
- 未考虑不同 Apple GPU 的最大线程限制差异
- M1/M2/M3 系列芯片的线程组大小限制和最优配置存在显著差异

**性能影响**：

- M1 Pro/Max: 最大 1024 线程/组，当前仅使用 256 线程（25% 利用率）
- M2 Ultra: 最大 1024 线程/组，存在线程组调度不均衡
- 固定配置导致小图像时线程浪费，大图像时调度次数过多

### 1.2 数据流转效率问题

**现状分析**：

```swift
// 当前低效流程
CGImage -> [UInt8] -> MTLTexture -> GPU计算 -> MTLBuffer -> CPU读取
```

**问题识别**：

1. **双重内存拷贝**：`CGContext.draw()` + `texture.replace()`
2. **同步阻塞**：每个通道独立的 `waitUntilCompleted()`
3. **资源重复创建**：每次计算重新分配 `ssimMapBuffer` 和 `resultBuffer`

### 1.3 GPU 计算效率问题

**现状分析**：

- 三次独立的内核分派（R、G、B 通道）
- 每次分派都需要重新加载纹理数据到共享内存
- CPU 端完成最终的归约求和，增加数据回读开销

## 2. 优化方案架构设计

### 2.1 自适应线程调度系统

**核心思路**：根据硬件能力和图像尺寸动态调整线程配置

```swift
struct AdaptiveThreadConfig {
    let threadsPerThreadgroup: MTLSize
    let threadgroupsPerGrid: MTLSize
    let optimalOccupancy: Float

    static func optimal(for device: MTLDevice, imageSize: CGSize) -> AdaptiveThreadConfig {
        let maxThreadsPerGroup = device.maxThreadsPerThreadgroup
        let simdWidth = device.simdWidth // Apple GPU 通常为 32

        // 基于图像尺寸和硬件能力计算最优配置
        let optimalThreadsX = min(nextPowerOf2(Int(imageSize.width)), maxThreadsPerGroup)
        let optimalThreadsY = min(maxThreadsPerGroup / optimalThreadsX, Int(imageSize.height))

        return AdaptiveThreadConfig(
            threadsPerThreadgroup: MTLSize(width: optimalThreadsX, height: optimalThreadsY, depth: 1),
            threadgroupsPerGrid: MTLSize(
                width: (Int(imageSize.width) + optimalThreadsX - 1) / optimalThreadsX,
                height: (Int(imageSize.height) + optimalThreadsY - 1) / optimalThreadsY,
                depth: 1
            ),
            optimalOccupancy: Float(optimalThreadsX * optimalThreadsY) / Float(maxThreadsPerGroup)
        )
    }
}
```

**关键优化点**：

1. **硬件感知**：检测 `device.maxThreadsPerThreadgroup` 和 `device.simdWidth`
2. **图像尺寸自适应**：小图像使用较小线程组，大图像最大化并行度
3. **内存对齐优化**：确保共享内存访问模式与 SIMD 宽度对齐

### 2.2 零拷贝数据流管线

**核心思路**：利用 `CVMetalTextureCache` 实现直接的 GPU 纹理映射

```swift
class TextureManager {
    private let device: MTLDevice
    private var textureCache: CVMetalTextureCache?
    private let pixelBufferPool: CVPixelBufferPool

    init(device: MTLDevice) throws {
        self.device = device
        CVMetalTextureCacheCreate(kCFAllocatorDefault, nil, device, nil, &textureCache)

        // 创建像素缓冲池以重用内存
        let poolAttributes: [String: Any] = [
            kCVPixelBufferPoolMinimumBufferCountKey as String: 3,
            kCVPixelBufferPoolMaximumBufferAgeKey as String: 0
        ]

        let pixelBufferAttributes: [String: Any] = [
            kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA,
            kCVPixelBufferMetalCompatibilityKey as String: true
        ]

        CVPixelBufferPoolCreate(kCFAllocatorDefault, poolAttributes as CFDictionary,
                               pixelBufferAttributes as CFDictionary, &pixelBufferPool)
    }

    func createTexture(from cgImage: CGImage) throws -> MTLTexture {
        // 直接从 CGImage 创建 CVPixelBuffer，避免中间数组
        var pixelBuffer: CVPixelBuffer?
        CVPixelBufferPoolCreatePixelBuffer(kCFAllocatorDefault, pixelBufferPool, &pixelBuffer)

        // 使用 CVPixelBufferLockBaseAddress 进行零拷贝写入
        CVPixelBufferLockBaseAddress(pixelBuffer!, [])
        defer { CVPixelBufferUnlockBaseAddress(pixelBuffer!, []) }

        let context = CGContext(
            data: CVPixelBufferGetBaseAddress(pixelBuffer!),
            width: cgImage.width,
            height: cgImage.height,
            bitsPerComponent: 8,
            bytesPerRow: CVPixelBufferGetBytesPerRow(pixelBuffer!),
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipFirst.rawValue
        )

        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: cgImage.width, height: cgImage.height))

        // 直接从 CVPixelBuffer 创建 Metal 纹理
        var cvMetalTexture: CVMetalTexture?
        CVMetalTextureCacheCreateTextureFromImage(
            kCFAllocatorDefault, textureCache!, pixelBuffer!, nil,
            .bgra8Unorm, cgImage.width, cgImage.height, 0, &cvMetalTexture
        )

        return CVMetalTextureGetTexture(cvMetalTexture!)!
    }
}
```

### 2.3 统一多通道计算内核

**核心思路**：重写 Metal 着色器，一次性处理 RGB 三通道

```metal
// 优化后的统一内核结构
kernel void unifiedSSIMKernel(
    texture2d<half, access::read> image1 [[texture(0)]],
    texture2d<half, access::read> image2 [[texture(1)]],
    device float3* ssim_results [[buffer(0)]],  // 直接输出 RGB 三通道结果
    constant float &C1 [[buffer(1)]],
    constant float &C2 [[buffer(2)]],
    constant AdaptiveThreadConfig &config [[buffer(3)]],
    uint2 tg_pos [[threadgroup_position_in_grid]],
    uint2 tt_pos [[thread_position_in_threadgroup]],
    uint2 tg_size [[threads_per_threadgroup]]
) {
    // 动态共享内存分配（基于实际线程组大小）
    const uint sharedX = tg_size.x + 2 * HALO;
    const uint sharedY = tg_size.y + 2 * HALO;

    threadgroup half3 sTile[sharedY][sharedX][2];  // 存储 RGB 三通道
    threadgroup float3 xconv[sharedY][tg_size.x][5];  // 中间结果也是三通道

    // 一次性加载和处理三个通道
    const uint W = image1.get_width();
    const uint H = image1.get_height();

    // 1. 加载瓦片数据（三通道同时处理）
    const int tileStartY = tg_pos.y * tg_size.y;
    const int tileStartX = tg_pos.x * tg_size.x;
    const int thread_rank = tt_pos.y * tg_size.x + tt_pos.x;

    for (int i = thread_rank; i < sharedX * sharedY; i += tg_size.x * tg_size.y) {
        int local_y = i / sharedX;
        int local_x = i % sharedX;
        int gy = tileStartY + local_y - HALO;
        int gx = tileStartX + local_x - HALO;

        half3 X, Y;
        if (gx < 0 || gx >= int(W) || gy < 0 || gy >= int(H)) {
            X = half3(0.0h);
            Y = half3(0.0h);
        } else {
            half4 p1 = image1.read(uint2(gx, gy));
            half4 p2 = image2.read(uint2(gx, gy));
            X = p1.rgb;
            Y = p2.rgb;
        }

        sTile[local_y][local_x][0] = X;
        sTile[local_y][local_x][1] = Y;
    }

    threadgroup_barrier(mem_flags::mem_threadgroup);

    // 2. 水平卷积（三通道并行）
    // ... 类似原实现，但处理 float3 而非单个 float

    // 3. 垂直卷积 + SSIM 计算
    // ... 最终输出三通道 SSIM 值

    const uint pix_x = tg_pos.x * tg_size.x + tt_pos.x;
    const uint pix_y = tg_pos.y * tg_size.y + tt_pos.y;
    const uint pix_id = pix_y * W + pix_x;

    if (pix_x < W && pix_y < H) {
        // 计算三通道 SSIM 并直接写入结果
        float3 ssim_rgb = calculateSSIM_RGB(out0, out1, out2, out3, out4, C1, C2);
        ssim_results[pix_id] = ssim_rgb;
    }
}
```

## 3. 实施路径与优先级

### 阶段一：核心架构重构（预期提升 5-8 倍）

1. **自适应线程调度系统**：重写 `MetalStateCache` 和 `MetalBridge`
2. **零拷贝纹理管理**：替换 `ImageProcessor` 的数据流
3. **异步命令提交**：消除同步阻塞点

### 阶段二：GPU 内核优化（预期额外提升 2-3 倍）

1. **统一多通道内核**：重写 `SSIMKernels.metal`
2. **完整 GPU 归约**：消除 CPU 端求和
3. **内存访问优化**：优化共享内存布局

### 阶段三：高级优化（预期额外提升 2-2.5 倍）

1. **资源池化管理**：减少 Metal 对象创建开销
2. **内核融合**：合并预处理和 SSIM 计算
3. **全局 half 精度优化**：全面采用 `half` 精度计算

## 4. 风险评估与缓解策略

### 4.1 计算精度风险

**风险**：全局使用 `half` 类型可能影响 SSIM 计算精度
**缓解**：SSIM 作为感知质量指标，half 精度完全满足需求；添加数值稳定性保护机制

### 4.2 硬件兼容性风险

**风险**：不同 Apple GPU 的特性差异可能导致性能回退
**缓解**：实现渐进式回退机制，确保在所有硬件上至少达到当前性能

### 4.3 内存压力风险

**风险**：零拷贝和资源池可能增加内存占用
**缓解**：实现智能内存管理和资源回收机制

## 5. 预期性能提升

基于分析和 Apple GPU 特性，预期优化效果：

| 优化项目       | 预期提升倍数 | 累积提升 |
| -------------- | ------------ | -------- |
| 自适应线程调度 | 2.5x         | 2.5x     |
| 零拷贝数据流   | 3.0x         | 7.5x     |
| 统一多通道计算 | 1.8x         | 13.5x    |
| GPU 完整归约   | 1.2x         | 16.2x    |
| 全局 half 精度 | 2.0x         | 32.4x    |

**总体预期**：在 Apple Silicon 平台上实现 **20-30 倍**的性能提升，使 Metal 实现显著超越原始 CUDA 版本的相对性能水平。

## 6. 详细技术实施指南

### 6.1 自适应线程调度实现细节

**硬件检测与配置**：

```swift
extension MTLDevice {
    var optimalSSIMConfig: AdaptiveThreadConfig {
        // Apple GPU 特性检测
        let isAppleSilicon = self.name.contains("Apple")
        let maxThreads = self.maxThreadsPerThreadgroup
        let simdWidth = isAppleSilicon ? 32 : 64  // Apple GPU vs AMD/Intel

        // 基于硬件特性的优化配置
        switch maxThreads {
        case 1024...:  // M1 Pro/Max, M2 系列
            return AdaptiveThreadConfig(baseThreads: 32, maxOccupancy: 0.875)
        case 512..<1024:  // M1 基础版
            return AdaptiveThreadConfig(baseThreads: 16, maxOccupancy: 0.75)
        default:  // 其他 GPU
            return AdaptiveThreadConfig(baseThreads: 16, maxOccupancy: 0.5)
        }
    }
}
```

**动态线程组计算**：

```swift
func calculateOptimalThreadgroups(imageSize: CGSize, device: MTLDevice) -> (MTLSize, MTLSize) {
    let config = device.optimalSSIMConfig
    let baseSize = config.baseThreads

    // 确保线程组大小是 SIMD 宽度的倍数
    let threadsX = min(nextMultipleOf(baseSize, device.simdWidth), Int(imageSize.width))
    let threadsY = min(device.maxThreadsPerThreadgroup / threadsX, Int(imageSize.height))

    let threadgroupSize = MTLSize(width: threadsX, height: threadsY, depth: 1)
    let gridSize = MTLSize(
        width: (Int(imageSize.width) + threadsX - 1) / threadsX,
        height: (Int(imageSize.height) + threadsY - 1) / threadsY,
        depth: 1
    )

    return (threadgroupSize, gridSize)
}
```

### 6.2 Metal 着色器优化实现

**共享内存动态分配**：

```metal
// 使用模板特化支持不同线程组大小
template<uint THREADS_X, uint THREADS_Y>
kernel void adaptiveSSIMKernel(
    texture2d<half, access::read> image1 [[texture(0)]],
    texture2d<half, access::read> image2 [[texture(1)]],
    device float3* results [[buffer(0)]],
    constant float &C1 [[buffer(1)]],
    constant float &C2 [[buffer(2)]],
    uint2 tg_pos [[threadgroup_position_in_grid]],
    uint2 tt_pos [[thread_position_in_threadgroup]]
) {
    constexpr uint HALO = 5;
    constexpr uint SHARED_X = THREADS_X + 2 * HALO;
    constexpr uint SHARED_Y = THREADS_Y + 2 * HALO;

    threadgroup half3 sTile[SHARED_Y][SHARED_X][2];
    threadgroup float3 xconv[SHARED_Y][THREADS_X][5];

    // 优化的数据加载模式
    loadTileOptimized<THREADS_X, THREADS_Y>(image1, image2, sTile, tg_pos, tt_pos);

    // 三通道并行卷积
    performTriChannelConvolution<THREADS_X, THREADS_Y>(sTile, xconv, tt_pos);

    // 最终 SSIM 计算
    computeFinalSSIM<THREADS_X, THREADS_Y>(xconv, results, C1, C2, tg_pos, tt_pos);
}
```

**内存访问模式优化**：

```metal
// 优化的瓦片加载函数
template<uint THREADS_X, uint THREADS_Y>
device void loadTileOptimized(
    texture2d<half, access::read> img1,
    texture2d<half, access::read> img2,
    threadgroup half3 sTile[][SHARED_X][2],
    uint2 tg_pos,
    uint2 tt_pos
) {
    const uint W = img1.get_width();
    const uint H = img1.get_height();
    const uint tileStartY = tg_pos.y * THREADS_Y;
    const uint tileStartX = tg_pos.x * THREADS_X;

    // 使用向量化加载减少内存事务
    const uint thread_rank = tt_pos.y * THREADS_X + tt_pos.x;
    const uint total_threads = THREADS_X * THREADS_Y;
    const uint elements_per_thread = (SHARED_X * SHARED_Y + total_threads - 1) / total_threads;

    for (uint i = 0; i < elements_per_thread; ++i) {
        uint idx = thread_rank + i * total_threads;
        if (idx >= SHARED_X * SHARED_Y) break;

        uint local_y = idx / SHARED_X;
        uint local_x = idx % SHARED_X;
        int gy = int(tileStartY) + int(local_y) - HALO;
        int gx = int(tileStartX) + int(local_x) - HALO;

        // 边界检查和数据加载
        if (gx >= 0 && gx < int(W) && gy >= 0 && gy < int(H)) {
            half4 p1 = img1.read(uint2(gx, gy));
            half4 p2 = img2.read(uint2(gx, gy));
            sTile[local_y][local_x][0] = p1.rgb;
            sTile[local_y][local_x][1] = p2.rgb;
        } else {
            sTile[local_y][local_x][0] = half3(0.0h);
            sTile[local_y][local_x][1] = half3(0.0h);
        }
    }
}
```

### 6.3 资源管理优化

**智能缓冲区池**：

```swift
class MetalResourcePool {
    private let device: MTLDevice
    private var bufferPools: [BufferDescriptor: [MTLBuffer]] = [:]
    private var texturePools: [TextureDescriptor: [MTLTexture]] = [:]
    private let poolLock = NSLock()

    struct BufferDescriptor: Hashable {
        let length: Int
        let storageMode: MTLStorageMode
        let usage: MTLResourceUsage
    }

    func borrowBuffer(length: Int, storageMode: MTLStorageMode = .shared) -> MTLBuffer {
        let descriptor = BufferDescriptor(length: length, storageMode: storageMode, usage: .shaderRead)

        poolLock.lock()
        defer { poolLock.unlock() }

        if var pool = bufferPools[descriptor], !pool.isEmpty {
            return pool.removeLast()
        }

        // 创建新缓冲区
        guard let buffer = device.makeBuffer(length: length, options: storageMode.resourceOptions) else {
            fatalError("Failed to create buffer")
        }

        return buffer
    }

    func returnBuffer(_ buffer: MTLBuffer) {
        let descriptor = BufferDescriptor(
            length: buffer.length,
            storageMode: buffer.storageMode,
            usage: buffer.resourceOptions
        )

        poolLock.lock()
        defer { poolLock.unlock() }

        if bufferPools[descriptor] == nil {
            bufferPools[descriptor] = []
        }

        // 限制池大小避免内存泄漏
        if bufferPools[descriptor]!.count < 10 {
            bufferPools[descriptor]!.append(buffer)
        }
    }
}
```

### 6.4 异步执行框架

**基于 Swift Concurrency 的异步计算**：

```swift
actor SSIMCalculationEngine {
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let resourcePool: MetalResourcePool
    private let textureManager: TextureManager

    func calculateSSIM(image1: CGImage, image2: CGImage) async throws -> Double {
        // 异步纹理创建
        async let texture1 = textureManager.createTexture(from: image1)
        async let texture2 = textureManager.createTexture(from: image2)

        let (tex1, tex2) = try await (texture1, texture2)

        // 创建计算命令
        guard let commandBuffer = commandQueue.makeCommandBuffer() else {
            throw SSIMError.metalKernelError("Failed to create command buffer")
        }

        // 配置自适应线程
        let (threadgroupSize, gridSize) = calculateOptimalThreadgroups(
            imageSize: CGSize(width: tex1.width, height: tex1.height),
            device: device
        )

        // 编码统一计算通道
        try encodeUnifiedSSIMComputation(
            commandBuffer: commandBuffer,
            texture1: tex1,
            texture2: tex2,
            threadgroupSize: threadgroupSize,
            gridSize: gridSize
        )

        // 异步执行
        return try await withCheckedThrowingContinuation { continuation in
            commandBuffer.addCompletedHandler { buffer in
                if buffer.status == .completed {
                    do {
                        let result = try self.extractResult(from: buffer)
                        continuation.resume(returning: result)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                } else {
                    continuation.resume(throwing: SSIMError.metalKernelError("GPU execution failed"))
                }
            }
            commandBuffer.commit()
        }
    }
}
```

## 7. 性能验证与基准测试

### 7.1 基准测试框架

```swift
struct SSIMBenchmark {
    static func runPerformanceTest(imageSize: CGSize, iterations: Int = 100) async -> BenchmarkResult {
        let calculator = try! SSIMCalculator()
        let testImages = generateTestImages(size: imageSize)

        var times: [Double] = []

        for _ in 0..<iterations {
            let startTime = CFAbsoluteTimeGetCurrent()
            _ = try! await calculator.calculate(from: testImages.0, to: testImages.1)
            let elapsedTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
            times.append(elapsedTime)
        }

        return BenchmarkResult(
            averageTime: times.reduce(0, +) / Double(times.count),
            minTime: times.min()!,
            maxTime: times.max()!,
            throughput: Double(iterations) / (times.reduce(0, +) / 1000)
        )
    }
}
```

### 7.2 回归测试保证

```swift
class SSIMAccuracyValidator {
    static func validateAgainstReference(tolerance: Double = 1e-6) throws {
        let referenceResults = loadReferenceResults()  // 从原始 CUDA 实现
        let testImages = loadTestImageSet()

        let calculator = try SSIMCalculator()

        for (imageSet, expectedSSIM) in zip(testImages, referenceResults) {
            let result = try calculator.calculate(from: imageSet.0, to: imageSet.1)
            let difference = abs(result.ssim - expectedSSIM)

            if difference > tolerance {
                throw ValidationError.accuracyMismatch(
                    expected: expectedSSIM,
                    actual: result.ssim,
                    difference: difference
                )
            }
        }
    }
}
```

## 8. 总结与后续规划

本优化方案通过**自适应线程调度**、**零拷贝数据流**和**统一计算管线**三大核心技术，预期在 Apple Silicon 平台上实现显著的性能提升。实施过程中需要特别注意计算精度保持和硬件兼容性，确保优化不会影响 SSIM 计算的准确性。

**下一步行动**：

1. 实施阶段一的核心架构重构
2. 建立完整的性能基准测试体系
3. 逐步验证和部署各项优化措施
4. 持续监控和调优性能表现
